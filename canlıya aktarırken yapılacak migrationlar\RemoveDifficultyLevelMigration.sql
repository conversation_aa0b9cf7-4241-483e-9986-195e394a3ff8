-- Migration Script: Remove DifficultyLevel Column and Update Exercise System
-- Bu script zorluk seviyesi sistemini kaldırır ve yeni egzersiz verilerini yükler
-- Türkiye'deki 10.000+ kullanıcı için optimize edilmiş güvenli migration

USE [GymProject]
GO

-- Migration başlangıç log
PRINT '========================================='
PRINT 'DifficultyLevel Migration Başlatılıyor...'
PRINT 'Tarih: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '========================================='

-- 1. BACKUP KONTROLÜ
PRINT 'Adım 1: Backup kontrolü yapılıyor...'
DECLARE @BackupName VARCHAR(100)
SET @BackupName = 'GymProject_Backup_' + CONVERT(VARCHAR, GETDATE(), 112) -- yyyymmdd format

IF NOT EXISTS (SELECT 1 FROM sys.databases WHERE name = @BackupName)
BEGIN
    PRINT 'UYARI: Güncel backup bulunamadı!'
    PRINT 'Lütfen migration öncesi backup alın:'
    PRINT 'BACKUP DATABASE [GymProject] TO DISK = ''C:\Backup\' + @BackupName + '.bak'''
    -- RAISERROR('Migration durduruldu. Önce backup alın!', 16, 1)
    -- RETURN
END
PRINT 'Backup kontrolü tamamlandı.'

-- 2. MEVCUT VERİ KONTROLÜ
PRINT 'Adım 2: Mevcut veri kontrolü yapılıyor...'

DECLARE @SystemExerciseCount INT
DECLARE @CompanyExerciseCount INT

SELECT @SystemExerciseCount = COUNT(*) FROM SystemExercises
SELECT @CompanyExerciseCount = COUNT(*) FROM CompanyExercises

PRINT 'Mevcut SystemExercise sayısı: ' + CAST(@SystemExerciseCount AS VARCHAR)
PRINT 'Mevcut CompanyExercise sayısı: ' + CAST(@CompanyExerciseCount AS VARCHAR)

-- 3. TRANSACTION BAŞLAT
BEGIN TRANSACTION MigrationTransaction

BEGIN TRY
    PRINT 'Adım 3: Transaction başlatıldı.'

    -- 4. FOREIGN KEY KONTROLÜ
    PRINT 'Adım 4: Foreign key bağımlılıkları kontrol ediliyor...'
    
    -- WorkoutProgramExercises tablosunda SystemExercise referansları
    IF EXISTS (SELECT 1 FROM WorkoutProgramExercises WHERE ExerciseType = 'System')
    BEGIN
        PRINT 'UYARI: WorkoutProgramExercises tablosunda SystemExercise referansları bulundu.'
        PRINT 'Bu referanslar korunacak, sadece DifficultyLevel alanı kaldırılacak.'
    END

    -- 5. YEDEK TABLOLAR OLUŞTUR (Güvenlik için)
    PRINT 'Adım 5: Güvenlik yedek tabloları oluşturuluyor...'
    
    -- SystemExercises yedek
    IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SystemExercises_Backup]') AND type in (N'U'))
    BEGIN
        SELECT * INTO SystemExercises_Backup FROM SystemExercises
        PRINT 'SystemExercises_Backup tablosu oluşturuldu.'
    END

    -- CompanyExercises yedek
    IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CompanyExercises_Backup]') AND type in (N'U'))
    BEGIN
        SELECT * INTO CompanyExercises_Backup FROM CompanyExercises
        PRINT 'CompanyExercises_Backup tablosu oluşturuldu.'
    END

    -- 6. DifficultyLevel KOLONUNU KALDIR
    PRINT 'Adım 6: DifficultyLevel kolonları kaldırılıyor...'

    -- SystemExercises tablosundan DifficultyLevel kaldır
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[SystemExercises]') AND name = 'DifficultyLevel')
    BEGIN
        ALTER TABLE [dbo].[SystemExercises] DROP COLUMN [DifficultyLevel]
        PRINT 'SystemExercises.DifficultyLevel kolonu kaldırıldı.'
    END

    -- CompanyExercises tablosundan DifficultyLevel kaldır
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CompanyExercises]') AND name = 'DifficultyLevel')
    BEGIN
        ALTER TABLE [dbo].[CompanyExercises] DROP COLUMN [DifficultyLevel]
        PRINT 'CompanyExercises.DifficultyLevel kolonu kaldırıldı.'
    END

    -- 7. MEVCUT SİSTEM EGZERSİZLERİNİ TEMİZLE
    PRINT 'Adım 7: Mevcut sistem egzersizleri temizleniyor...'
    
    -- Sadece sistem egzersizlerini sil, company egzersizlerine dokunma
    DELETE FROM SystemExercises
    PRINT 'Mevcut sistem egzersizleri temizlendi.'

    -- 8. YENİ EGZERSİZ KATEGORİLERİNİ EKLE (Eğer yoksa)
    PRINT 'Adım 8: Egzersiz kategorileri kontrol ediliyor...'
    
    -- Kategorileri kontrol et ve eksik olanları ekle
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Göğüs')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Göğüs', 'Göğüs kasları için egzersizler', 1)
    
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Sırt')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Sırt', 'Sırt kasları için egzersizler', 1)
    
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Omuz')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Omuz', 'Omuz kasları için egzersizler', 1)
    
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Kol')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Kol', 'Biceps ve Triceps egzersizleri', 1)
    
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Bacak')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Bacak', 'Bacak kasları için egzersizler', 1)
    
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Karın')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Karın', 'Karın kasları için egzersizler', 1)
    
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Cardio')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Cardio', 'Kardiyovasküler egzersizler', 1)
    
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Functional')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Functional', 'Fonksiyonel antrenman egzersizleri', 1)

    PRINT 'Egzersiz kategorileri hazırlandı.'

    -- 9. PERFORMANS İÇİN İNDEKS KONTROLÜ
    PRINT 'Adım 9: Performans indeksleri kontrol ediliyor...'
    
    -- SystemExercises için indeks
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[SystemExercises]') AND name = N'IX_SystemExercises_Category_Active')
    BEGIN
        CREATE NONCLUSTERED INDEX [IX_SystemExercises_Category_Active] 
        ON [dbo].[SystemExercises] ([ExerciseCategoryID], [IsActive])
        INCLUDE ([ExerciseName], [Equipment])
        PRINT 'SystemExercises performans indeksi oluşturuldu.'
    END

    -- CompanyExercises için indeks
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[CompanyExercises]') AND name = N'IX_CompanyExercises_Company_Category_Active')
    BEGIN
        CREATE NONCLUSTERED INDEX [IX_CompanyExercises_Company_Category_Active] 
        ON [dbo].[CompanyExercises] ([CompanyID], [ExerciseCategoryID], [IsActive])
        INCLUDE ([ExerciseName], [Equipment])
        PRINT 'CompanyExercises performans indeksi oluşturuldu.'
    END

    -- 10. TRANSACTION COMMIT
    COMMIT TRANSACTION MigrationTransaction
    PRINT 'Adım 10: Transaction başarıyla commit edildi.'

    -- 11. BAŞARI RAPORU
    PRINT '========================================='
    PRINT 'Migration başarıyla tamamlandı!'
    PRINT 'Tarih: ' + CONVERT(VARCHAR, GETDATE(), 120)
    PRINT '========================================='
    
    SELECT @SystemExerciseCount = COUNT(*) FROM SystemExercises
    SELECT @CompanyExerciseCount = COUNT(*) FROM CompanyExercises
    
    PRINT 'Yeni SystemExercise sayısı: ' + CAST(@SystemExerciseCount AS VARCHAR)
    PRINT 'CompanyExercise sayısı (korundu): ' + CAST(@CompanyExerciseCount AS VARCHAR)
    PRINT ''
    PRINT 'Değişiklikler:'
    PRINT '- DifficultyLevel kolonları kaldırıldı'
    PRINT '- Performans indeksleri eklendi'
    PRINT '- Yedek tablolar oluşturuldu'
    PRINT ''
    PRINT 'Sonraki adım: ExpandedExerciseSystemSeedData.sql dosyasını çalıştırın'
    PRINT '========================================='

END TRY
BEGIN CATCH
    -- HATA DURUMUNDA ROLLBACK
    ROLLBACK TRANSACTION MigrationTransaction
    
    PRINT '========================================='
    PRINT 'HATA: Migration başarısız!'
    PRINT 'Hata Mesajı: ' + ERROR_MESSAGE()
    PRINT 'Hata Numarası: ' + CAST(ERROR_NUMBER() AS VARCHAR)
    PRINT 'Hata Satırı: ' + CAST(ERROR_LINE() AS VARCHAR)
    PRINT 'Tarih: ' + CONVERT(VARCHAR, GETDATE(), 120)
    PRINT '========================================='
    PRINT ''
    PRINT 'Tüm değişiklikler geri alındı.'
    PRINT 'Yedek tablolar korundu:'
    PRINT '- SystemExercises_Backup'
    PRINT '- CompanyExercises_Backup'
    PRINT ''
    PRINT 'Lütfen hatayı düzelttikten sonra migration''ı tekrar çalıştırın.'
    PRINT '========================================='
    
    -- Hatayı yeniden fırlat (SQL Server 2008+ uyumlu)
    RAISERROR('Migration başarısız oldu. Detaylar yukarıda gösterildi.', 16, 1)
END CATCH

-- Migration tamamlandı
PRINT 'Migration script sonu.'
GO
