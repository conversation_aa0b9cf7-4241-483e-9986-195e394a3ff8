-- Migration Script: Remove DifficultyLevel Column (SQL Server 2008+ Compatible)
-- Bu script zorluk seviyesi sistemini kaldırır
-- Tüm SQL Server versiyonları ile uyumlu

USE [GymProject]
GO

-- Migration başlangıç log
PRINT '========================================='
PRINT 'DifficultyLevel Migration Başlatılıyor...'
PRINT 'Tarih: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '========================================='

-- Değişkenler
DECLARE @SystemExerciseCount INT
DECLARE @CompanyExerciseCount INT
DECLARE @ErrorMessage NVARCHAR(4000)
DECLARE @ErrorNumber INT
DECLARE @ErrorSeverity INT
DECLARE @ErrorState INT

-- Mevcut veri sayısı
SELECT @SystemExerciseCount = COUNT(*) FROM SystemExercises
SELECT @CompanyExerciseCount = COUNT(*) FROM CompanyExercises

PRINT 'Mevcut SystemExercise sayısı: ' + CAST(@SystemExerciseCount AS VARCHAR)
PRINT 'Mevcut CompanyExercise sayısı: ' + CAST(@CompanyExerciseCount AS VARCHAR)

-- Transaction başlat
BEGIN TRANSACTION MigrationTransaction

BEGIN TRY
    PRINT 'Transaction başlatıldı.'

    -- 1. YEDEK TABLOLAR OLUŞTUR
    PRINT 'Adım 1: Güvenlik yedek tabloları oluşturuluyor...'
    
    -- SystemExercises yedek (eğer yoksa)
    IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SystemExercises_Backup]') AND type in (N'U'))
    BEGIN
        SELECT * INTO SystemExercises_Backup FROM SystemExercises
        PRINT 'SystemExercises_Backup tablosu oluşturuldu.'
    END
    ELSE
    BEGIN
        PRINT 'SystemExercises_Backup tablosu zaten mevcut.'
    END

    -- CompanyExercises yedek (eğer yoksa)
    IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CompanyExercises_Backup]') AND type in (N'U'))
    BEGIN
        SELECT * INTO CompanyExercises_Backup FROM CompanyExercises
        PRINT 'CompanyExercises_Backup tablosu oluşturuldu.'
    END
    ELSE
    BEGIN
        PRINT 'CompanyExercises_Backup tablosu zaten mevcut.'
    END

    -- 2. DifficultyLevel KOLONUNU KALDIR
    PRINT 'Adım 2: DifficultyLevel kolonları kaldırılıyor...'

    -- SystemExercises tablosundan DifficultyLevel kaldır
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[SystemExercises]') AND name = 'DifficultyLevel')
    BEGIN
        ALTER TABLE [dbo].[SystemExercises] DROP COLUMN [DifficultyLevel]
        PRINT 'SystemExercises.DifficultyLevel kolonu kaldırıldı.'
    END
    ELSE
    BEGIN
        PRINT 'SystemExercises.DifficultyLevel kolonu zaten mevcut değil.'
    END

    -- CompanyExercises tablosundan DifficultyLevel kaldır
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CompanyExercises]') AND name = 'DifficultyLevel')
    BEGIN
        ALTER TABLE [dbo].[CompanyExercises] DROP COLUMN [DifficultyLevel]
        PRINT 'CompanyExercises.DifficultyLevel kolonu kaldırıldı.'
    END
    ELSE
    BEGIN
        PRINT 'CompanyExercises.DifficultyLevel kolonu zaten mevcut değil.'
    END

    -- 3. PERFORMANS İNDEKSLERİ EKLE
    PRINT 'Adım 3: Performans indeksleri kontrol ediliyor...'
    
    -- SystemExercises için indeks
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[SystemExercises]') AND name = N'IX_SystemExercises_Category_Active')
    BEGIN
        CREATE NONCLUSTERED INDEX [IX_SystemExercises_Category_Active] 
        ON [dbo].[SystemExercises] ([ExerciseCategoryID], [IsActive])
        INCLUDE ([ExerciseName], [Equipment])
        PRINT 'SystemExercises performans indeksi oluşturuldu.'
    END
    ELSE
    BEGIN
        PRINT 'SystemExercises performans indeksi zaten mevcut.'
    END

    -- CompanyExercises için indeks
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[CompanyExercises]') AND name = N'IX_CompanyExercises_Company_Category_Active')
    BEGIN
        CREATE NONCLUSTERED INDEX [IX_CompanyExercises_Company_Category_Active] 
        ON [dbo].[CompanyExercises] ([CompanyID], [ExerciseCategoryID], [IsActive])
        INCLUDE ([ExerciseName], [Equipment])
        PRINT 'CompanyExercises performans indeksi oluşturuldu.'
    END
    ELSE
    BEGIN
        PRINT 'CompanyExercises performans indeksi zaten mevcut.'
    END

    -- 4. EGZERSİZ KATEGORİLERİNİ KONTROL ET
    PRINT 'Adım 4: Egzersiz kategorileri kontrol ediliyor...'
    
    -- Kategorileri kontrol et ve eksik olanları ekle
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Göğüs')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Göğüs', 'Göğüs kasları için egzersizler', 1)
    
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Sırt')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Sırt', 'Sırt kasları için egzersizler', 1)
    
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Omuz')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Omuz', 'Omuz kasları için egzersizler', 1)
    
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Kol')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Kol', 'Biceps ve Triceps egzersizleri', 1)
    
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Bacak')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Bacak', 'Bacak kasları için egzersizler', 1)
    
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Karın')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Karın', 'Karın kasları için egzersizler', 1)
    
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Cardio')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Cardio', 'Kardiyovasküler egzersizler', 1)
    
    IF NOT EXISTS (SELECT 1 FROM ExerciseCategories WHERE CategoryName = 'Functional')
        INSERT INTO ExerciseCategories (CategoryName, Description, IsActive) VALUES ('Functional', 'Fonksiyonel antrenman egzersizleri', 1)

    PRINT 'Egzersiz kategorileri hazırlandı.'

    -- Transaction commit
    COMMIT TRANSACTION MigrationTransaction
    PRINT 'Transaction başarıyla commit edildi.'

    -- Başarı raporu
    PRINT '========================================='
    PRINT 'Migration başarıyla tamamlandı!'
    PRINT 'Tarih: ' + CONVERT(VARCHAR, GETDATE(), 120)
    PRINT '========================================='
    
    SELECT @SystemExerciseCount = COUNT(*) FROM SystemExercises
    SELECT @CompanyExerciseCount = COUNT(*) FROM CompanyExercises
    
    PRINT 'SystemExercise sayısı: ' + CAST(@SystemExerciseCount AS VARCHAR)
    PRINT 'CompanyExercise sayısı: ' + CAST(@CompanyExerciseCount AS VARCHAR)
    PRINT ''
    PRINT 'Değişiklikler:'
    PRINT '- DifficultyLevel kolonları kaldırıldı'
    PRINT '- Performans indeksleri eklendi'
    PRINT '- Yedek tablolar oluşturuldu'
    PRINT ''
    PRINT 'Sonraki adım: ExpandedExerciseSystemSeedData.sql dosyasını çalıştırın'
    PRINT '========================================='

END TRY
BEGIN CATCH
    -- Hata durumunda rollback
    ROLLBACK TRANSACTION MigrationTransaction
    
    -- Hata bilgilerini al
    SELECT 
        @ErrorNumber = ERROR_NUMBER(),
        @ErrorSeverity = ERROR_SEVERITY(),
        @ErrorState = ERROR_STATE(),
        @ErrorMessage = ERROR_MESSAGE()
    
    PRINT '========================================='
    PRINT 'HATA: Migration başarısız!'
    PRINT 'Hata Mesajı: ' + @ErrorMessage
    PRINT 'Hata Numarası: ' + CAST(@ErrorNumber AS VARCHAR)
    PRINT 'Hata Seviyesi: ' + CAST(@ErrorSeverity AS VARCHAR)
    PRINT 'Hata Durumu: ' + CAST(@ErrorState AS VARCHAR)
    PRINT 'Tarih: ' + CONVERT(VARCHAR, GETDATE(), 120)
    PRINT '========================================='
    PRINT ''
    PRINT 'Tüm değişiklikler geri alındı.'
    PRINT 'Yedek tablolar korundu:'
    PRINT '- SystemExercises_Backup'
    PRINT '- CompanyExercises_Backup'
    PRINT ''
    PRINT 'Lütfen hatayı düzelttikten sonra migration''ı tekrar çalıştırın.'
    PRINT '========================================='
    
    -- Hatayı yeniden fırlat (SQL Server 2008+ uyumlu)
    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState)
END CATCH

-- Migration tamamlandı
PRINT 'Migration script sonu.'
GO
